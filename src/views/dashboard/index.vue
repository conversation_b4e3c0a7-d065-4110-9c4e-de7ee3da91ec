<script setup lang="ts">
defineOptions({
  name: 'Dashboard'
});
</script>

<template>
  <div class="min-h-500px flex-center">
    <div class="flex-col-center">
      <div class="text-400px text-primary">
        <icon-local-logo />
      </div>
      <p class="text-24px font-bold">{{ $t('page.dashboard.title') }}</p>
      <p class="text-#999">{{ $t('page.dashboard.desc') }}</p>
    </div>
  </div>
</template>

<style scoped></style>
